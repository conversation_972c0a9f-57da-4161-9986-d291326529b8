/* ===== APEX 3D FLIPBOOK SYSTEM - CSS =====
   المبرمج الأسطوري - نظام كتاب ثلاثي الأبعاد واقعي
   تصميم متقدم مع تقليب طبيعي للصفحات
*/

/* ===== 3D FLIPBOOK CORE VARIABLES - RESPONSIVE ===== */
:root {
    --flipbook-width: clamp(12rem, 25vw, 17.5rem);
    --flipbook-height: clamp(16rem, 35vw, 25rem);
    --flipbook-depth: clamp(1rem, 2vw, 1.25rem);
    --page-thickness: clamp(0.125rem, 0.25vw, 0.125rem);
    --animation-duration: 0.8s;
    --animation-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --page-color: #fefefe;
    --cover-color: #8b4513;
    --spine-color: #654321;
    --text-color: #333;
    --primary-color: #ffda37;
    --base-font-size: clamp(0.75rem, 1.5vw, 1rem);
}

/* ===== FLIPBOOK CARD CONTAINER ===== */
.flipbook-card {
    position: relative;
    width: var(--flipbook-width);
    height: var(--flipbook-height);
    perspective: clamp(75rem, 150vw, 93.75rem);
    transform-style: preserve-3d;
    cursor: pointer;
    margin: clamp(1rem, 3vw, 2rem);
    transition: all var(--animation-duration) var(--animation-easing);
    user-select: none;
    /* Enhanced responsive behavior */
    flex-shrink: 0;
    scroll-snap-align: center;
    /* Performance optimizations */
    will-change: transform;
    backface-visibility: hidden;
}

/* ===== FLIPBOOK STATES ===== */
.flipbook-card.closed {
    transform: rotateY(0deg) rotateX(0deg);
    z-index: 1;
}

.flipbook-card.opening {
    transform: rotateY(-5deg) rotateX(2deg) scale(1.05);
    z-index: 1000;
    position: relative;
}

.flipbook-card.open {
    transform: rotateY(-15deg) rotateX(5deg) scale(1.1);
    z-index: 1000;
    position: relative;
}

.flipbook-card.closing {
    transform: rotateY(-5deg) rotateX(2deg) scale(1.05);
    z-index: 1000;
    position: relative;
}

/* ===== FLIPBOOK CONTAINER ===== */
.flipbook-container {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: all var(--animation-duration) var(--animation-easing);
}

/* ===== BOOK COVER ===== */
.flipbook-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transform-origin: left center;
    transition: transform var(--animation-duration) var(--animation-easing);
    z-index: 20;
    border-radius: 8px;
    box-shadow: 
        0 0 0 2px var(--cover-color),
        0 10px 30px var(--shadow-color),
        0 20px 60px rgba(0, 0, 0, 0.2);
}

/* Cover Front */
.cover-front {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--cover-color) 0%, #654321 100%);
    border-radius: 8px;
    backface-visibility: hidden;
    overflow: hidden;
}

.cover-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    filter: contrast(1.1) saturate(1.1) brightness(0.95);
}

/* Cover Overlay */
.cover-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        transparent 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
    border-radius: 0 0 8px 8px;
}

.book-info {
    color: white;
    text-align: center;
}

.book-title {
    font-family: 'Cairo', sans-serif;
    font-size: clamp(0.8rem, 2vw, 1.1rem);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 clamp(0.25rem, 1vw, 0.5rem) 0;
    text-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.8);
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    max-width: 100%;
    text-overflow: ellipsis;
}

.book-author {
    font-family: 'Cairo', sans-serif;
    font-size: clamp(0.7rem, 1.5vw, 0.9rem);
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.8);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    max-width: 100%;
    text-overflow: ellipsis;
}

/* ===== BOOK RATING - أعلى يمين كل كتاب ===== */
.book-rating {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.85);
    color: var(--primary-color);
    padding: 5px 8px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 218, 55, 0.4);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
    z-index: 20; /* أعلى من جميع العناصر */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 3px;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    letter-spacing: 0.3px;
}

/* Cover Back */
.cover-back {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f5dc 0%, #e6e6d4 100%);
    border-radius: 8px;
    transform: rotateY(180deg);
    backface-visibility: hidden;
    padding: 30px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1);
}

.back-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    color: var(--text-color);
}

.back-content h4 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--cover-color);
}

.back-content p {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    line-height: 1.6;
    color: #555;
}

/* 📱 تحسين back-content للأجهزة الصغيرة */
@media (max-width: 768px) {
    .back-content p {
        font-family: 'Inter', sans-serif;
        font-size: 0.6rem !important;
        line-height: 1.2 !important;
        color: #555;
    }
}

@media (max-width: 480px) {
    .back-content p {
        font-family: 'Inter', sans-serif;
        font-size: 0.6rem !important;
        line-height: 1.2 !important;
        color: #555;
    }
}

/* ===== BOOK PAGES ===== */
.flipbook-pages {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

.flipbook-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transform-origin: left center;
    transition: transform var(--animation-duration) var(--animation-easing);
    border-radius: 6px;
    z-index: 10;
}

/* Page Front and Back */
.page-front,
.page-back {
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--page-color);
    border-radius: 6px;
    backface-visibility: hidden;
    box-shadow: 
        0 0 0 1px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.15);
}

.page-back {
    transform: rotateY(180deg);
}

/* Page Content */
.page-content {
    padding: 25px;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: var(--text-color);
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    max-height: 100%;
}

.page-content h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--cover-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

/* ===== BOOK SPINE ===== */
.flipbook-spine {
    position: absolute;
    left: -var(--flipbook-depth);
    top: 0;
    width: var(--flipbook-depth);
    height: 100%;
    background: linear-gradient(
        180deg,
        var(--spine-color) 0%,
        #4a2c17 50%,
        var(--spine-color) 100%
    );
    transform: rotateY(-90deg);
    transform-origin: right center;
    border-radius: 0 0 0 8px;
    box-shadow: 
        inset -4px 0 8px rgba(0, 0, 0, 0.6),
        0 0 15px rgba(0, 0, 0, 0.4);
}

/* ===== FLIP HINT ===== */
.flip-hint {
    position: absolute;
    bottom: var(--flip-hint-distance); /* 🎯 استخدام المتغير للتحكم السهل */
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-family: 'Cairo', sans-serif;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 218, 55, 0.3);
    z-index: 10;
    white-space: nowrap;
}

.flip-hint i {
    margin-left: 5px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* Hide flip hint when book is open */
.flipbook-card.open .flip-hint,
.flipbook-card.opening .flip-hint,
.flipbook-card.closing .flip-hint {
    opacity: 0 !important;
    visibility: hidden;
    pointer-events: none;
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.flipbook-card.closed {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.flipbook-card.closed:hover {
    transform: translateY(-12px) rotateY(-5deg) scale(1.02);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(255, 218, 55, 0.2);
}

.flipbook-card.closed:hover .flip-hint {
    opacity: 1;
    transform: translateX(-50%) translateY(5px);
}

.flipbook-card.closed:hover .book-rating {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(255, 218, 55, 0.4);
}

/* ===== NAVIGATION CONTROLS - ENHANCED POSITIONING ===== */
.flipbook-navigation {
    position: absolute;
    bottom: var(--navigation-distance); /* 🎯 استخدام المتغير للتحكم السهل */
    left: 50%;
    transform: translateX(-50%);
    display: none; /* Hidden by default */
    align-items: center;
    gap: clamp(0.5rem, 1.5vw, 0.75rem);
    background: rgba(0, 0, 0, 0.95);
    padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(0.75rem, 2vw, 1rem);
    border-radius: clamp(1rem, 3vw, 1.5rem);
    backdrop-filter: blur(1rem);
    border: 1px solid rgba(255, 218, 55, 0.4);
    box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.5);
    z-index: 10000;
    min-width: fit-content;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Show navigation when book is open - ONLY when not on cover */
.flipbook-card.open .flipbook-navigation,
.flipbook-card.opening .flipbook-navigation {
    display: none; /* مخفي افتراضياً حتى لو كان الكتاب مفتوح */
    opacity: 0;
    visibility: hidden;
}

/* إظهار التنقل فقط عندما نكون في صفحة غير الغلاف */
.flipbook-card.open[data-current-page="1"] .flipbook-navigation,
.flipbook-card.open[data-current-page="2"] .flipbook-navigation,
.flipbook-card.opening[data-current-page="1"] .flipbook-navigation,
.flipbook-card.opening[data-current-page="2"] .flipbook-navigation {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ===== MOBILE SPECIFIC NAVIGATION POSITIONING ===== */
/* Small screens - positioned below book without overlap */
@media (max-width: 768px) {
    .flipbook-navigation {
        bottom: var(--navigation-distance-tablet) !important; /* 🎯 استخدام متغير التابلت */
        padding: 0.4rem 0.6rem !important;
        gap: 0.4rem !important;
        border-radius: 0.8rem !important;
        font-size: 0.8rem !important;
    }

    .flip-hint {
        bottom: var(--flip-hint-distance-tablet) !important; /* 🎯 استخدام متغير flip-hint للتابلت */
    }
}

/* Extra small screens - maintain clear separation */
@media (max-width: 480px) {
    .flipbook-navigation {
        bottom: var(--navigation-distance-mobile) !important; /* 🎯 استخدام متغير الموبايل */
        padding: 0.3rem 0.5rem !important;
        gap: 0.3rem !important;
        border-radius: 0.6rem !important;
        font-size: 0.75rem !important;
    }

    .flip-hint {
        bottom: var(--flip-hint-distance-mobile) !important; /* 🎯 استخدام متغير flip-hint للموبايل */
    }
}

/* Very small screens - minimal distance */
@media (max-width: 320px) {
    .flipbook-navigation {
        bottom: -0.2rem !important; /* Minimal distance */
        padding: 0.25rem 0.4rem !important;
        gap: 0.25rem !important;
        border-radius: 0.5rem !important;
        font-size: 0.7rem !important;
    }
}

.nav-btn {
    background: transparent;
    border: 0.125rem solid var(--primary-color);
    color: var(--primary-color);
    width: clamp(2rem, 4vw, 2.5rem);
    height: clamp(2rem, 4vw, 2.5rem);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: clamp(0.75rem, 1.5vw, 0.9rem);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    /* Enhanced touch support */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    min-width: 2.75rem;
    min-height: 2.75rem;
}

/* ===== MOBILE SPECIFIC BUTTON SIZING ===== */
@media (max-width: 768px) {
    .nav-btn {
        width: 2rem !important;
        height: 2rem !important;
        min-width: 2rem !important;
        min-height: 2rem !important;
        font-size: 0.7rem !important;
        border-width: 1px !important;
    }
}

@media (max-width: 480px) {
    .nav-btn {
        width: 1.8rem !important;
        height: 1.8rem !important;
        min-width: 1.8rem !important;
        min-height: 1.8rem !important;
        font-size: 0.65rem !important;
    }
}

@media (max-width: 320px) {
    .nav-btn {
        width: 1.6rem !important;
        height: 1.6rem !important;
        min-width: 1.6rem !important;
        min-height: 1.6rem !important;
        font-size: 0.6rem !important;
    }
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: #000;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 218, 55, 0.4);
}

.nav-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    border-color: #666;
    color: #666;
}

.page-indicator {
    color: var(--primary-color);
    font-family: 'Cairo', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

/* ===== MOBILE SPECIFIC PAGE INDICATOR ===== */
@media (max-width: 768px) {
    .page-indicator {
        font-size: 0.7rem !important;
        min-width: 40px !important;
    }
}

@media (max-width: 480px) {
    .page-indicator {
        font-size: 0.65rem !important;
        min-width: 35px !important;
    }
}

@media (max-width: 320px) {
    .page-indicator {
        font-size: 0.6rem !important;
        min-width: 30px !important;
    }
}

.close-btn {
    background: rgba(220, 53, 69, 0.2);
    border-color: #dc3545;
    color: #dc3545;
}

.close-btn:hover {
    background: #dc3545;
    color: white;
}

/* ===== TABLE OF CONTENTS ===== */
.table-of-contents {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.toc-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: rgba(255, 218, 55, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.toc-item:hover {
    background: rgba(255, 218, 55, 0.2);
    transform: translateX(5px);
}

.chapter-number {
    background: var(--primary-color);
    color: #000;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    margin-left: 12px;
}

.chapter-title {
    flex: 1;
    font-weight: 500;
    color: var(--text-color);
}

.page-number {
    color: var(--cover-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== BOOK METADATA ===== */
.book-metadata {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.book-metadata p {
    margin: 0;
    padding: 12px;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--cover-color);
}

.book-metadata strong {
    color: var(--cover-color);
    font-weight: 600;
}

/* ===== CHAPTER CONTENT ===== */
.chapter-content {
    flex: 1;
    margin-top: 20px;
    line-height: 1.6;
}

.chapter-content p {
    margin-bottom: 15px;
    text-align: justify;
}

.chapter-content ul {
    margin: 15px 0;
    padding-right: 20px;
}

.chapter-content li {
    margin-bottom: 8px;
    position: relative;
}

.chapter-content li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    right: -15px;
}

/* ===== MEDICAL DIAGRAM ===== */
.medical-diagram {
    margin: 20px 0;
    text-align: center;
}

.diagram-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed var(--primary-color);
    border-radius: 12px;
    padding: 40px 20px;
    color: var(--cover-color);
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
}

/* ===== PAGE NUMBER ===== */
.page-number {
    position: absolute;
    bottom: 15px;
    right: 20px;
    background: var(--primary-color);
    color: #000;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* ===== BOOK ACTIONS ===== */
.book-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 30px;
}

/* 🚫 تم نقل تصميم action-btn إلى قسم Actions Page - السطر 2206 */

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 218, 55, 0.4);
}

.download-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.download-btn:hover {
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.bookmark-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.bookmark-btn:hover {
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.share-btn {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.share-btn:hover {
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
}

/* ===== THANK YOU CONTENT ===== */
.thank-you-content {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.library-info {
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 218, 55, 0.1);
    border-radius: 12px;
    border: 2px solid rgba(255, 218, 55, 0.3);
}

.library-info p {
    margin: 8px 0;
    color: var(--cover-color);
    font-weight: 500;
}

/* ===== ADVANCED 3D EFFECTS ===== */

/* Realistic page shadows during flip */
.flipbook-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 0, 0, 0.05) 30%,
        rgba(0, 0, 0, 0.1) 70%,
        rgba(0, 0, 0, 0.15) 100%);
    opacity: 0;
    transition: opacity var(--animation-duration) ease;
    pointer-events: none;
    border-radius: 6px;
}

/* Show shadow during page flip */
.flipbook-page[style*="rotateY"]::before {
    opacity: 1;
}

/* Page curl effect */
.flipbook-page::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(-45deg, transparent 50%, rgba(0, 0, 0, 0.1) 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0 6px 0 0;
}

.flipbook-card.open .flipbook-page::after {
    opacity: 1;
}

/* Spine shadow effect */
.flipbook-spine::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(0, 0, 0, 0.3) 0%,
        transparent 50%,
        rgba(0, 0, 0, 0.1) 100%);
    border-radius: 0 0 0 8px;
}

/* Book depth illusion */
.flipbook-container::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: -2px;
    bottom: -2px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: -1;
    transform: translateZ(-5px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large screens (1200px+) */
@media (min-width: 1200px) {
    .flipbook-card {
        width: 320px;
        height: 450px;
    }

    .page-content {
        padding: 35px;
    }

    .book-title {
        font-size: 1.3rem;
    }

    .action-btn {
        padding: 18px 25px;
        font-size: 1.1rem;
    }
}

/* Medium screens (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
    .flipbook-card {
        width: 260px;
        height: 380px;
    }

    .page-content {
        padding: 25px;
    }

    .book-title {
        font-size: 1rem;
    }

    .book-author {
        font-size: 0.85rem;
    }
}

/* Small screens (480px - 767px) */
@media (max-width: 767px) and (min-width: 480px) {
    .flipbook-card {
        width: 220px;
        height: 320px;
        margin: 1.5rem;
    }

    .page-content {
        padding: 20px;
    }

    .book-title {
        font-size: 0.95rem;
    }

    .book-author {
        font-size: 0.8rem;
    }

    .action-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    /* Old navigation removed */
}

/* Extra small screens (320px - 479px) */
@media (max-width: 479px) {
    .flipbook-card {
        width: 180px;
        height: 260px;
        margin: 1rem;
    }

    .page-content {
        padding: 15px;
    }

    .book-title {
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .book-author {
        font-size: 0.75rem;
    }

    .book-rating {
        top: 10px;
        right: 10px;
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .action-btn {
        padding: 10px 12px;
        font-size: 0.8rem;
    }

    /* Old navigation removed */

    .page-indicator {
        font-size: 0.8rem;
        min-width: 50px;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus states for keyboard navigation */
.flipbook-card:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 5px;
}

.nav-btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.action-btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .flipbook-card,
    .flipbook-cover,
    .flipbook-page,
    .nav-btn,
    .action-btn {
        transition: none;
        animation: none;
    }

    .flip-hint i {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .flipbook-card {
        border: 2px solid;
    }

    .nav-btn {
        border-width: 3px;
    }

    .action-btn {
        border: 2px solid;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .flipbook-card {
        transform: none !important;
        perspective: none;
        page-break-inside: avoid;
    }

    .flipbook-navigation,
    .flip-hint {
        display: none !important;
    }

    .page-content {
        background: white !important;
        color: black !important;
    }
}

/* ===== ADVANCED REALISTIC PAGE FLIPPING ===== */

/* Page flip states with natural curve */
.flipbook-page[data-page="1"] {
    z-index: 15;
    transform: rotateY(0deg);
}

.flipbook-page[data-page="2"] {
    z-index: 14;
    transform: rotateY(0deg);
}

.flipbook-page[data-page="3"] {
    z-index: 13;
    transform: rotateY(0deg);
}

.flipbook-page[data-page="4"] {
    z-index: 12;
    transform: rotateY(0deg);
}

/* Natural page curve during flip */
.flipbook-page.flipping {
    animation: naturalPageFlip var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes naturalPageFlip {
    0% {
        transform: rotateY(0deg) rotateX(0deg);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    25% {
        transform: rotateY(-45deg) rotateX(2deg) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    }
    50% {
        transform: rotateY(-90deg) rotateX(5deg) scale(1.05);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.35);
    }
    75% {
        transform: rotateY(-135deg) rotateX(3deg) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    }
    100% {
        transform: rotateY(-180deg) rotateX(0deg);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

/* Reverse flip animation */
.flipbook-page.flipping-back {
    animation: naturalPageFlipBack var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes naturalPageFlipBack {
    0% {
        transform: rotateY(-180deg) rotateX(0deg);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    25% {
        transform: rotateY(-135deg) rotateX(3deg) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    }
    50% {
        transform: rotateY(-90deg) rotateX(5deg) scale(1.05);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.35);
    }
    75% {
        transform: rotateY(-45deg) rotateX(2deg) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    }
    100% {
        transform: rotateY(0deg) rotateX(0deg);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

/* Page bend effect during flip */
.flipbook-page.flipping::before,
.flipbook-page.flipping-back::before {
    opacity: 1;
    background: linear-gradient(90deg,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0.05) 30%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 70%,
        rgba(255, 255, 255, 0.2) 100%);
}

/* Realistic binding shadow */
.flipbook-page.flipped {
    transform: rotateY(-180deg);
    z-index: 5;
}

.flipbook-page.flipped::after {
    content: '';
    position: absolute;
    left: -5px;
    top: 0;
    width: 10px;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        transparent 100%);
    z-index: 1;
    border-radius: 0 0 0 6px;
}

/* Cover opening animation */
.flipbook-cover.opening {
    animation: coverOpen var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes coverOpen {
    0% {
        transform: rotateY(0deg);
        box-shadow: 0 10px 30px var(--shadow-color);
    }
    25% {
        transform: rotateY(-45deg) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
    50% {
        transform: rotateY(-90deg) scale(1.05);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    }
    75% {
        transform: rotateY(-135deg) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
    100% {
        transform: rotateY(-180deg);
        box-shadow: 0 10px 30px var(--shadow-color);
    }
}

/* Cover closing animation */
.flipbook-cover.closing {
    animation: coverClose var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes coverClose {
    0% {
        transform: rotateY(-180deg);
        box-shadow: 0 10px 30px var(--shadow-color);
    }
    25% {
        transform: rotateY(-135deg) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
    50% {
        transform: rotateY(-90deg) scale(1.05);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    }
    75% {
        transform: rotateY(-45deg) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
    100% {
        transform: rotateY(0deg);
        box-shadow: 0 10px 30px var(--shadow-color);
    }
}

/* RTL Support - Right to Left Reading */
.flipbook-card[dir="rtl"] .flipbook-cover,
.flipbook-card[dir="rtl"] .flipbook-page {
    transform-origin: right center;
}

.flipbook-card[dir="rtl"] .flipbook-cover.opening {
    animation: coverOpenRTL var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes coverOpenRTL {
    0% {
        transform: rotateY(0deg);
    }
    25% {
        transform: rotateY(45deg) scale(1.02);
    }
    50% {
        transform: rotateY(90deg) scale(1.05);
    }
    75% {
        transform: rotateY(135deg) scale(1.02);
    }
    100% {
        transform: rotateY(180deg);
    }
}

.flipbook-card[dir="rtl"] .flipbook-page.flipping {
    animation: naturalPageFlipRTL var(--animation-duration) var(--animation-easing) forwards;
}

@keyframes naturalPageFlipRTL {
    0% {
        transform: rotateY(0deg) rotateX(0deg);
    }
    25% {
        transform: rotateY(45deg) rotateX(2deg) scale(1.02);
    }
    50% {
        transform: rotateY(90deg) rotateX(5deg) scale(1.05);
    }
    75% {
        transform: rotateY(135deg) rotateX(3deg) scale(1.02);
    }
    100% {
        transform: rotateY(180deg) rotateX(0deg);
    }
}

/* Enhanced page thickness illusion */
.flipbook-page {
    border-right: var(--page-thickness) solid rgba(0, 0, 0, 0.1);
    border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.flipbook-card[dir="rtl"] .flipbook-page {
    border-left: var(--page-thickness) solid rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

/* Realistic paper texture */
.page-front,
.page-back {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 0, 0, 0.02) 0%, transparent 50%),
        linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.03) 50%, transparent 51%);
    background-size: 100px 100px, 150px 150px, 20px 20px;
}

/* Subtle page aging effect */
.page-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(139, 69, 19, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(139, 69, 19, 0.01) 0%, transparent 50%);
    pointer-events: none;
    border-radius: 6px;
}

/* ===== ADVANCED RESPONSIVE DESIGN ===== */

/* Ultra-wide screens (1920px+) */
@media (min-width: 1920px) {
    .flipbook-card {
        width: 360px;
        height: 500px;
    }

    .page-content {
        padding: 40px;
        font-size: 1.1rem;
    }

    .book-title {
        font-size: 1.5rem;
    }

    .book-author {
        font-size: 1.1rem;
    }

    .action-btn {
        padding: 20px 30px;
        font-size: 1.2rem;
    }

    .flipbook-navigation {
        bottom: -70px;
        padding: 15px 25px;
    }

    .nav-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Large tablets landscape (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) and (orientation: landscape) {
    .flipbook-card {
        width: 280px;
        height: 400px;
    }

    .page-content {
        padding: 30px;
    }

    .flipbook-navigation {
        bottom: -55px;
    }
}

/* Tablets portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) and (orientation: portrait) {
    .flipbook-card {
        width: 240px;
        height: 340px;
        margin: 1.5rem;
    }

    .page-content {
        padding: 22px;
        font-size: 0.95rem;
    }

    .book-title {
        font-size: 1rem;
        line-height: 1.2;
    }

    .book-author {
        font-size: 0.85rem;
    }

    .action-btn {
        padding: 14px 18px;
        font-size: 0.95rem;
    }

    .chapter-content {
        font-size: 0.9rem;
        line-height: 1.5;
    }
}

/* Small tablets and large phones landscape (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) and (orientation: landscape) {
    .flipbook-card {
        width: 200px;
        height: 280px;
        margin: 1rem;
    }

    .page-content {
        padding: 18px;
        font-size: 0.85rem;
    }

    .book-title {
        font-size: 0.9rem;
    }

    .book-author {
        font-size: 0.75rem;
    }

    .action-btn {
        padding: 10px 14px;
        font-size: 0.85rem;
        gap: 6px;
    }

    .flipbook-navigation {
        bottom: -40px;
        padding: 8px 12px;
        gap: 8px;
    }

    .nav-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
}

/* Large phones portrait (480px - 639px) */
@media (max-width: 639px) and (min-width: 480px) and (orientation: portrait) {
    .flipbook-card {
        width: 200px;
        height: 300px;
        margin: 1rem;
    }

    .page-content {
        padding: 20px;
        font-size: 0.9rem;
    }

    .book-title {
        font-size: 0.95rem;
        line-height: 1.2;
    }

    .book-author {
        font-size: 0.8rem;
    }

    .book-rating {
        top: 12px;
        right: 12px;
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .action-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .table-of-contents {
        gap: 8px;
    }

    .toc-item {
        padding: 8px;
    }

    .chapter-number {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }
}

/* Medium phones (375px - 479px) */
@media (max-width: 479px) and (min-width: 375px) {
    .flipbook-card {
        width: 170px;
        height: 250px;
        margin: 0.8rem;
    }

    .page-content {
        padding: 16px;
        font-size: 0.8rem;
    }

    .book-title {
        font-size: 0.85rem;
        line-height: 1.1;
    }

    .book-author {
        font-size: 0.7rem;
    }

    .book-rating {
        top: 8px;
        right: 8px;
        padding: 4px 8px;
        font-size: 0.65rem;
    }

    .action-btn {
        padding: 10px 12px;
        font-size: 0.8rem;
        gap: 4px;
    }

    .flipbook-navigation {
        bottom: -35px;
        padding: 6px 10px;
        gap: 6px;
    }

    .nav-btn {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .page-indicator {
        font-size: 0.7rem;
        min-width: 40px;
    }

    .chapter-content {
        font-size: 0.75rem;
        line-height: 1.4;
    }

    .chapter-content ul {
        padding-right: 15px;
    }
}

/* Small phones (320px - 374px) */
@media (max-width: 374px) {
    .flipbook-card {
        width: 150px;
        height: 220px;
        margin: 0.5rem;
    }

    .page-content {
        padding: 12px;
        font-size: 0.75rem;
    }

    .book-title {
        font-size: 0.8rem;
        line-height: 1.1;
        margin-bottom: 4px;
    }

    .book-author {
        font-size: 0.65rem;
    }

    .book-rating {
        top: 6px;
        right: 6px;
        padding: 3px 6px;
        font-size: 0.6rem;
    }

    .action-btn {
        padding: 8px 10px;
        font-size: 0.75rem;
        gap: 3px;
    }

    .flipbook-navigation {
        bottom: -30px;
        padding: 5px 8px;
        gap: 5px;
    }

    .nav-btn {
        width: 24px;
        height: 24px;
        font-size: 0.65rem;
        border-width: 1px;
    }

    .page-indicator {
        font-size: 0.65rem;
        min-width: 35px;
    }

    .chapter-content {
        font-size: 0.7rem;
        line-height: 1.3;
    }

    .toc-item {
        padding: 6px;
    }

    .chapter-number {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        margin-left: 8px;
    }

    .medical-diagram {
        margin: 15px 0;
    }

    .diagram-placeholder {
        padding: 20px 10px;
        font-size: 0.9rem;
    }
}

/* ===== DEVICE-SPECIFIC OPTIMIZATIONS ===== */

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .flipbook-card {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    .cover-image img {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Touch devices */
@media (hover: none) and (pointer: coarse) {
    .flipbook-card {
        cursor: default;
    }

    .nav-btn,
    .action-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .flipbook-card.closed:hover {
        transform: none;
    }

    .flip-hint {
        display: none;
    }

    /* Larger touch targets */
    .action-btn {
        padding: 14px 20px;
        margin: 8px 0;
    }
}

/* Devices with limited hover capability */
@media (hover: hover) and (pointer: fine) {
    .flipbook-card:hover .cover-overlay {
        background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(0, 0, 0, 0.5) 50%,
            transparent 100%
        );
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --page-color: #2a2a2a;
        --text-color: #e0e0e0;
        --cover-color: #5a3a1a;
        --spine-color: #4a2a0a;
    }

    .page-front,
    .page-back {
        background: var(--page-color);
        color: var(--text-color);
    }

    .cover-back {
        background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
    }

    .book-metadata p {
        background: rgba(90, 58, 26, 0.2);
    }

    .toc-item {
        background: rgba(255, 218, 55, 0.15);
    }
}

/* ===== FINAL POLISH AND ENHANCEMENTS ===== */

/* Smooth loading animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(clamp(1rem, 3vw, 1.875rem));
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.flipbook-card {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== CRITICAL RESPONSIVE FIXES ===== */

/* Ensure flipbook cards are always visible and properly sized */
.flipbook-card {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    /* Force responsive dimensions */
    min-width: clamp(10rem, 20vw, 15rem) !important;
    max-width: clamp(15rem, 30vw, 20rem) !important;
    min-height: clamp(14rem, 28vw, 22rem) !important;
    max-height: clamp(20rem, 40vw, 30rem) !important;
}

/* 🚫 تم حذف القاعدة المتضاربة - الاعتماد على القاعدة الأساسية فقط */

/* 🚫 تم حذف القواعد المتضاربة - الاعتماد على القواعد الأساسية فقط */

/* Fix text overflow in all flipbook elements */
.flipbook-card * {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    -webkit-hyphens: auto !important;
    -ms-hyphens: auto !important;
}

/* Ensure proper text sizing in pages */
.page-content {
    font-size: clamp(0.75rem, 1.5vw, 1rem) !important;
    line-height: 1.5 !important;
    padding: clamp(1rem, 3vw, 1.5rem) !important;
}

.page-content h3 {
    font-size: clamp(1rem, 2.5vw, 1.3rem) !important;
    line-height: 1.3 !important;
    margin-bottom: clamp(0.75rem, 2vw, 1rem) !important;
}

/* Fix action buttons sizing */
.action-btn {
    font-size: clamp(0.8rem, 1.5vw, 1rem) !important;
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem) !important;
    margin: clamp(0.5rem, 1vw, 0.75rem) 0 !important;
}

/* ===== ENHANCED BOOK RATING - SMART RESPONSIVE ===== */
.book-rating {
    font-size: clamp(0.65rem, 1.25vw, 0.8rem) !important;
    padding: clamp(0.25rem, 1vw, 0.375rem) clamp(0.5rem, 1.5vw, 0.75rem) !important;
    top: clamp(0.5rem, 2vw, 1rem) !important;
    right: clamp(0.5rem, 2vw, 1rem) !important;
    /* تحسينات إضافية للوضوح */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
    min-width: fit-content;
}

/* Stagger animation for multiple books */
.flipbook-card:nth-child(1) { animation-delay: 0.1s; }
.flipbook-card:nth-child(2) { animation-delay: 0.2s; }
.flipbook-card:nth-child(3) { animation-delay: 0.3s; }
.flipbook-card:nth-child(4) { animation-delay: 0.4s; }
.flipbook-card:nth-child(5) { animation-delay: 0.5s; }

/* Enhanced focus indicators for accessibility */
.flipbook-card:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 5px;
    box-shadow: 0 0 0 6px rgba(255, 218, 55, 0.3);
}

/* Loading state */
.flipbook-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.flipbook-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid rgba(255, 218, 55, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.flipbook-card.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

.flipbook-card.error::before {
    content: '⚠️ خطأ في التحميل';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 1000;
}

/* Success feedback */
.flipbook-card.success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Enhanced page curl effect */
.flipbook-page:hover::after {
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

/* Realistic book depth shadows */
.flipbook-container::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0.05) 50%,
        transparent 100%);
    border-radius: 8px;
    z-index: -2;
}

/* Page turn sound effect indicator */
.flipbook-page.turning::before {
    content: '📄';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.2rem;
    opacity: 0;
    animation: soundWave 0.5s ease-out;
}

@keyframes soundWave {
    0% { opacity: 0; transform: scale(0.5); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0; transform: scale(1); }
}

/* Enhanced navigation buttons */
.nav-btn {
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.nav-btn:hover::before {
    width: 100%;
    height: 100%;
}

/* Book collection grid enhancements */
.books-grid {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.books-grid::-webkit-scrollbar {
    height: 8px;
}

.books-grid::-webkit-scrollbar-track {
    background: rgba(255, 218, 55, 0.1);
    border-radius: 4px;
}

.books-grid::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.books-grid::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
    opacity: 0.8;
}

/* Performance optimization classes */
.gpu-accelerated {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.reduce-motion {
    animation: none !important;
    transition: none !important;
}

/* Final quality assurance */
.flipbook-card * {
    box-sizing: border-box;
}

.flipbook-card img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* ===== ENHANCED Z-INDEX STACKING ===== */
.books-grid {
    position: relative;
    z-index: 1;
}

.flipbook-card {
    position: relative;
    z-index: 2;
}

.flipbook-card.closed {
    z-index: 2;
}

.flipbook-card.opening,
.flipbook-card.open,
.flipbook-card.closing {
    z-index: 9999 !important;
    position: relative;
}

.flipbook-navigation {
    z-index: 10000;
    position: relative;
}

.flip-hint {
    z-index: 10001;
    position: relative;
}

/* Ensure open book appears above all other content */
.flipbook-card.open {
    isolation: isolate;
}

/* Fix container stacking context */
.books-section {
    position: relative;
    z-index: 1;
}

.books-section:has(.flipbook-card.open) {
    z-index: 9998;
}

/* ===== REALISTIC INTERNAL PAGES STYLING ===== */

/* Title Page Styling */
.title-page {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
    padding: 30px 25px 25px 25px;
    height: 100%;
    overflow: hidden;
}

.book-title-section {
    margin-bottom: 30px;
    flex-shrink: 0;
}

.main-book-title {
    font-family: 'Cairo', sans-serif;
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 15px 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
}

.book-author-name {
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    color: #ffffff;
    margin: 0 0 20px 0;
    opacity: 0.9;
    word-wrap: break-word;
}

.book-description-preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 20px;
}

.book-description-preview h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    color: var(--cover-color);
    margin: 0 0 12px 0;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 6px;
    flex-shrink: 0;
}

.description-text {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
    text-align: justify;
    margin: 0;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    flex: 1;
}

/* Description Pages Styling */
.description-page {
    padding: 25px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.description-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: visible; /* Allow content to be fully visible */
    max-height: 100%;
}

.description-content h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--cover-color);
    margin: 0 0 15px 0;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
    flex-shrink: 0;
}

.description-text-content {
    flex: 1;
    overflow-y: auto; /* Enable scrolling for long descriptions */
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    max-height: calc(100% - 6rem); /* Reserve space for header and margins */
}

.description-text-content p {
    font-family: 'Inter', sans-serif;
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-color);
    text-align: justify;
    margin: 0;
    text-indent: 15px;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
}

/* ===== ACTIONS PAGE STYLING - RESPONSIVE DESIGN ===== */

/* Desktop Design */
.actions-page {
    padding: clamp(20px, 4vw, 30px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;
}

.actions-page h3 {
    font-family: 'Cairo', sans-serif;
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    font-weight: 600;
    color: var(--cover-color);
    text-align: center;
    margin-bottom: clamp(15px, 3vw, 25px);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: clamp(8px, 1.5vw, 12px);
    width: 100%;
}

.book-actions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: clamp(12px, 2.5vw, 18px);
    width: 100%;
    max-width: 280px;
}

/* ===== ACTION BUTTONS - RESPONSIVE DESIGN ===== */

/* Desktop Action Buttons */
.action-btn {
    background: linear-gradient(135deg, var(--primary-color), #e6c533);
    border: none;
    color: #000;
    padding: clamp(15px, 3vw, 20px);
    border-radius: clamp(10px, 2vw, 12px);
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(6px, 1.5vw, 8px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(255, 218, 55, 0.3);
    width: 100%;
    min-height: clamp(80px, 15vw, 100px);
}

.action-btn i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.action-btn span {
    font-size: 1.1rem;
    font-weight: 600;
}

.action-btn small {
    font-size: clamp(0.7rem, 1.5vw, 0.8rem);
    opacity: 0.8;
    font-weight: 400;
    line-height: 1.2;
}

/* ===== TABLET RESPONSIVE DESIGN (768px وأقل) ===== */
@media (max-width: 768px) {
    .actions-page {
        padding: 15px;
        justify-content: center;
    }

    .actions-page h3 {
        font-size: 1rem;
        margin-bottom: 15px;
        padding-bottom: 8px;
    }

    .book-actions-grid {
        gap: 12px;
        max-width: 250px;
    }

    .action-btn {
        padding: 12px;
        min-height: 70px;
        border-radius: 8px;
    }

    .action-btn i {
        font-size: 1.2rem;
    }

    .action-btn span {
        font-size: 0.9rem;
    }

    .action-btn small {
        font-size: 0.65rem;
    }
}

/* ===== MOBILE RESPONSIVE DESIGN (480px وأقل) - تصميم ثابت مخصص ===== */
@media (max-width: 480px) {
    .actions-page {
        padding: 8px !important;
        justify-content: center !important;
        gap: 8px;
    }

    .actions-page h3 {
        font-size: 0.8rem !important;
        margin-bottom: 8px !important;
        padding-bottom: 4px !important;
        border-bottom: 1px solid var(--primary-color) !important;
        font-weight: 500 !important;
    }

    .book-actions-grid {
        gap: 6px !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .action-btn {
        padding: 8px 6px !important;
        min-height: 50px !important;
        border-radius: 6px !important;
        gap: 3px !important;
        box-shadow: 0 2px 8px rgba(255, 218, 55, 0.2) !important;
    }

    .action-btn i {
        font-size: 0.9rem !important;
        margin-bottom: 2px !important;
    }

    .action-btn span {
        font-size: 0.65rem !important;
        font-weight: 500 !important;
        line-height: 1.1 !important;
        margin-bottom: 1px !important;
    }

    .action-btn small {
        font-size: 0.5rem !important;
        opacity: 0.7 !important;
        line-height: 1.0 !important;
        font-weight: 300 !important;
    }

    /* إزالة التأثيرات للموبايل */
    .action-btn:hover {
        transform: none !important;
        box-shadow: 0 2px 8px rgba(255, 218, 55, 0.3) !important;
    }
}

/* ===== EXTRA SMALL MOBILE (320px وأقل) - تصميم مضغوط ===== */
@media (max-width: 320px) {
    .actions-page {
        padding: 6px !important;
        gap: 6px !important;
    }

    .actions-page h3 {
        font-size: 0.7rem !important;
        margin-bottom: 6px !important;
        padding-bottom: 3px !important;
    }

    .book-actions-grid {
        gap: 4px !important;
    }

    .action-btn {
        padding: 6px 4px !important;
        min-height: 45px !important;
        border-radius: 4px !important;
        gap: 2px !important;
    }

    .action-btn i {
        font-size: 0.8rem !important;
        margin-bottom: 1px !important;
    }

    .action-btn span {
        font-size: 0.6rem !important;
        margin-bottom: 0px !important;
    }

    .action-btn small {
        font-size: 0.45rem !important;
        opacity: 0.6 !important;
    }
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 218, 55, 0.5);
}

.view-book-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.view-book-btn:hover {
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.5);
}

.download-book-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.download-book-btn:hover {
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.5);
}

/* Secondary Actions - 🚫 تم حذف العنصر من HTML لكن CSS محفوظ للتوافق */
.additional-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.secondary-btn {
    flex: 1;
    background: rgba(255, 218, 55, 0.1);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.secondary-btn:hover {
    background: var(--primary-color);
    color: #000;
    transform: translateY(-2px);
}

/* Enhanced Internal Page Navigation */
.page-navigation-internal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 218, 55, 0.3);
    gap: 15px;
}

.nav-left,
.nav-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.next-page-internal,
.prev-page-internal,
.close-book-btn {
    background: var(--primary-color);
    border: none;
    color: #000;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 100px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.next-page-internal::before,
.prev-page-internal::before,
.close-book-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.next-page-internal:hover::before,
.prev-page-internal:hover::before,
.close-book-btn:hover::before {
    left: 100%;
}

.next-page-internal:hover,
.prev-page-internal:hover {
    background: #e6c533;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 218, 55, 0.4);
}

.close-book-btn {
    background: rgba(220, 53, 69, 0.8);
    color: white;
}

.close-book-btn:hover {
    background: #dc3545;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    transform: translateY(-2px);
}

.back-to-cover-btn {
    background: rgba(108, 117, 125, 0.8);
    color: white;
}

.back-to-cover-btn:hover {
    background: #6c757d;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
    transform: translateY(-2px);
}

/* Read more hint */
.read-more-hint {
    font-style: italic;
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-top: 15px;
    text-align: center;
    opacity: 0.8;
}

/* Library stats in thank you page */
.library-stats {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.library-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(255, 218, 55, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.library-stats .stat-item i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.library-stats .stat-item span {
    font-family: 'Inter', sans-serif;
    font-size: 0.85rem;
    color: var(--text-color);
    font-weight: 500;
}

/* ===== RESPONSIVE TEXT SYSTEM - APEX EDITION ===== */

/* Base text system with fluid scaling */
.flipbook-card {
    --base-font-size: 0.8rem;
    --title-scale: 1.6;
    --author-scale: 1.1;
    --description-scale: 0.9;
    --heading-scale: 1.2;
}

/* Ensure all text elements respect page boundaries */
.page-content * {
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
}

/* Prevent text overflow in all page types */
.title-page,
.description-page,
.actions-page {
    max-height: 100%;
    overflow: hidden;
}

/* Ensure content areas don't exceed page bounds */
.description-content,
.book-title-section,
.book-description-preview,
.book-actions-grid,
.additional-actions,
.thank-you-section {
    max-width: 100%;
    overflow: hidden;
}

/* Responsive text scaling based on container size */
.main-book-title {
    font-size: calc(var(--base-font-size) * var(--title-scale));
    line-height: 1.2;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.book-author-name {
    font-size: calc(var(--base-font-size) * var(--author-scale));
    line-height: 1.3;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.description-text,
.description-text-content p {
    font-size: clamp(0.7rem, 2vw, 0.9rem); /* Dynamic responsive scaling */
    line-height: 1.4;
    max-width: 100%;
    overflow-y: auto; /* Allow scrolling if needed */
    overflow-x: hidden;
    max-height: calc(100% - 4rem); /* Ensure content fits in page */
    word-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    text-align: justify;
    padding-right: 0.5rem; /* Space for scrollbar */
}

.description-text {
    -webkit-line-clamp: 6; /* Preview text */
}

.description-text-content p {
    -webkit-line-clamp: 12; /* Full description */
}

.book-description-preview h3,
.description-content h3 {
    font-size: calc(var(--base-font-size) * var(--heading-scale));
    line-height: 1.3;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== PROGRESSIVE RESPONSIVE SCALING ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .flipbook-card {
        --base-font-size: 1.1rem;
        --title-scale: 1.8;
        --author-scale: 1.3;
        --description-scale: 1.0;
        --heading-scale: 1.4;
    }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .flipbook-card {
        --base-font-size: 1rem;
        --title-scale: 1.6;
        --author-scale: 1.2;
        --description-scale: 0.95;
        --heading-scale: 1.3;
    }
}

/* Laptop (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .flipbook-card {
        --base-font-size: 0.95rem;
        --title-scale: 1.5;
        --author-scale: 1.1;
        --description-scale: 0.9;
        --heading-scale: 1.2;
    }
}

/* ===== SMART RESPONSIVE BREAKPOINTS - تنسيقات ذكية ===== */

/* Large Tablet (769px - 991px) */
@media (max-width: 991px) and (min-width: 769px) {
    .flipbook-card {
        --base-font-size: 0.9rem;
        --title-scale: 1.4;
        --author-scale: 1.0;
        --description-scale: 0.85;
        --heading-scale: 1.1;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(200px, calc(25% - 1rem)), 1fr)) !important;
        gap: clamp(1rem, 2.5vw, 1.5rem) !important;
    }

    .flipbook-card {
        max-width: 220px;
        margin-bottom: 2.5rem !important;
    }

    .book-rating {
        top: 12px;
        right: 12px;
        padding: 4px 8px;
        font-size: 0.75rem;
    }
}

/* Medium Tablet (641px - 768px) - تنسيق ذكي */
@media (max-width: 768px) and (min-width: 641px) {
    .flipbook-card {
        --base-font-size: 0.8rem;
        --title-scale: 1.3;
        --author-scale: 0.95;
        --description-scale: 0.8;
        --heading-scale: 1.05;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(180px, calc(33.333% - 0.8rem)), 1fr)) !important;
        gap: clamp(0.8rem, 2vw, 1.2rem) !important;
        padding: clamp(0.5rem, 1.5vw, 0.8rem) !important;
    }

    .flipbook-card {
        max-width: min(180px, calc(33.333vw - 1rem));
        margin-bottom: 2.2rem !important;
        min-width: 160px;
    }

    .flipbook-card .page-content {
        padding: 10px !important;
    }

    .book-rating {
        top: 10px;
        right: 10px;
        padding: 3px 7px;
        font-size: 0.7rem;
        border-radius: 8px;
    }

    .actions-page {
        padding: 12px !important;
    }

    .actions-page h3 {
        font-size: 0.9rem !important;
        margin-bottom: 12px !important;
    }

    .action-btn {
        padding: 10px !important;
        min-height: 60px !important;
        font-size: 0.8rem !important;
    }
}

/* Small Tablet (481px - 640px) - تنسيق ذكي */
@media (max-width: 640px) and (min-width: 481px) {
    .flipbook-card {
        --base-font-size: 0.75rem;
        --title-scale: 1.25;
        --author-scale: 0.9;
        --description-scale: 0.75;
        --heading-scale: 1.0;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(160px, calc(50% - 0.6rem)), 1fr)) !important;
        gap: clamp(0.6rem, 1.8vw, 1rem) !important;
        padding: clamp(0.4rem, 1.2vw, 0.6rem) !important;
    }

    .flipbook-card {
        max-width: min(160px, calc(50% - 0.8rem));
        margin-bottom: 2rem !important;
        min-width: 140px;
    }

    .flipbook-card .page-content {
        padding: 9px !important;
    }

    .flipbook-card .book-title {
        font-size: calc(var(--base-font-size) * 1.1) !important;
        line-height: 1.15 !important;
        margin-bottom: 5px !important;
    }

    .flipbook-card .book-author {
        font-size: calc(var(--base-font-size) * 0.9) !important;
        margin-bottom: 5px !important;
    }

    .book-rating {
        top: 8px;
        right: 8px;
        padding: 3px 6px;
        font-size: 0.65rem;
        border-radius: 6px;
    }

    .actions-page {
        padding: 10px !important;
    }

    .actions-page h3 {
        font-size: 0.85rem !important;
        margin-bottom: 10px !important;
    }

    .action-btn {
        padding: 9px 7px !important;
        min-height: 55px !important;
        gap: 4px !important;
    }

    .action-btn span {
        font-size: 0.7rem !important;
    }

    .action-btn small {
        font-size: 0.55rem !important;
    }

    .book-rating {
        top: 8px;
        right: 8px;
        padding: 3px 6px;
        font-size: 0.65rem;
        border-radius: 10px;
    }
}

/* ===== LARGE MOBILE (561px - 640px) - بين الموبايل والتابلت ===== */
@media (max-width: 640px) and (min-width: 561px) {
    .flipbook-card {
        --base-font-size: 0.78rem;
        --title-scale: 1.28;
        --author-scale: 0.92;
        --description-scale: 0.78;
        --heading-scale: 1.02;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(170px, calc(50% - 0.7rem)), 1fr)) !important;
        gap: clamp(0.7rem, 1.9vw, 1.1rem) !important;
        padding: clamp(0.45rem, 1.3vw, 0.7rem) !important;
    }

    .flipbook-card {
        max-width: min(170px, calc(50% - 0.9rem));
        margin-bottom: 2.1rem !important;
        min-width: 150px;
    }

    .book-rating {
        top: 9px;
        right: 9px;
        padding: 4px 7px;
        font-size: 0.68rem;
        border-radius: 11px;
    }

    .actions-page {
        padding: 11px !important;
    }

    .action-btn {
        padding: 9px 8px !important;
        min-height: 57px !important;
        gap: 4px !important;
    }
}

/* ===== MEDIUM MOBILE (521px - 560px) - بين الموبايل والتابلت ===== */
@media (max-width: 560px) and (min-width: 521px) {
    .flipbook-card {
        --base-font-size: 0.76rem;
        --title-scale: 1.26;
        --author-scale: 0.91;
        --description-scale: 0.76;
        --heading-scale: 1.01;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(155px, calc(50% - 0.65rem)), 1fr)) !important;
        gap: clamp(0.65rem, 1.7vw, 1rem) !important;
        padding: clamp(0.4rem, 1.2vw, 0.65rem) !important;
    }

    .flipbook-card {
        max-width: min(155px, calc(50% - 0.85rem));
        margin-bottom: 2rem !important;
        min-width: 145px;
    }

    .book-rating {
        top: 8px;
        right: 8px;
        padding: 3px 6px;
        font-size: 0.66rem;
        border-radius: 10px;
    }

    .actions-page {
        padding: 10px !important;
    }

    .action-btn {
        padding: 8px 7px !important;
        min-height: 54px !important;
        gap: 3px !important;
    }
}

/* ===== ENHANCED MOBILE TRANSITIONS (481px - 640px) ===== */
@media (max-width: 640px) and (min-width: 481px) {
    /* تحسين flip-hint للأحجام المتوسطة */
    .flip-hint {
        bottom: var(--flip-hint-distance-tablet) !important;
        font-size: 0.65rem !important;
        padding: 4px 8px !important;
        border-radius: 10px !important;
    }

    /* تحسين navigation للأحجام المتوسطة */
    .flipbook-navigation {
        bottom: var(--navigation-distance-tablet) !important;
        padding: 0.35rem 0.5rem !important;
        gap: 0.35rem !important;
        border-radius: 0.7rem !important;
        font-size: 0.75rem !important;
    }

    /* تحسين أزرار التنقل */
    .nav-btn {
        width: 1.9rem !important;
        height: 1.9rem !important;
        min-width: 1.9rem !important;
        min-height: 1.9rem !important;
        font-size: 0.68rem !important;
    }
}

/* ===== LARGE SCREEN OPTIMIZATIONS (992px+) ===== */
@media (min-width: 992px) {
    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(280px, calc(20% - 1.5rem)), 1fr)) !important;
        gap: clamp(1.5rem, 3vw, 2.5rem) !important;
        padding: clamp(1rem, 2.5vw, 1.5rem) !important;
    }

    .flipbook-card {
        max-width: 320px;
        margin-bottom: 3rem !important;
    }

    .book-rating {
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 20px;
    }

    .actions-page {
        padding: 25px !important;
    }

    .actions-page h3 {
        font-size: 1.3rem !important;
        margin-bottom: 20px !important;
    }

    .action-btn {
        padding: 18px !important;
        min-height: 90px !important;
        font-size: 1rem !important;
    }
}

/* ===== ULTRA WIDE SCREENS (1400px+) ===== */
@media (min-width: 1400px) {
    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(300px, calc(16.666% - 2rem)), 1fr)) !important;
        gap: clamp(2rem, 3.5vw, 3rem) !important;
        max-width: 1600px;
        margin: 0 auto;
    }

    .flipbook-card {
        max-width: 350px;
        margin-bottom: 3.5rem !important;
    }

    .book-rating {
        top: 18px;
        right: 18px;
        padding: 8px 14px;
        font-size: 0.85rem;
    }
}

/* Small height adjustments */
@media (max-height: 600px) {
    .flipbook-card {
        --base-font-size: 0.85rem;
        --title-scale: 1.3;
        --author-scale: 0.95;
        --description-scale: 0.8;
        --heading-scale: 1.0;
    }

    .page-content {
        padding: 20px;
    }
}

@media (max-height: 500px) {
    .flipbook-card {
        --base-font-size: 0.8rem;
        --title-scale: 1.2;
        --author-scale: 0.9;
        --description-scale: 0.75;
        --heading-scale: 0.95;
    }

    .page-content {
        padding: 15px;
    }

    .description-text-content p {
        -webkit-line-clamp: 10;
    }

    .description-text {
        -webkit-line-clamp: 5;
    }
}

/* Metadata Styling */
.book-metadata-detailed h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--cover-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.metadata-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(255, 218, 55, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.metadata-label {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: var(--cover-color);
}

.metadata-value {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    font-weight: 500;
}

/* Additional Info Styling */
.additional-info h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--cover-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.book-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 218, 55, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.stat-item i {
    color: var(--primary-color);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.stat-item span {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    font-weight: 500;
}

/* Thank You Section */
.thank-you-section h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--cover-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.appreciation-content {
    text-align: center;
}

.appreciation-content p {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 20px;
}

.contact-info h4 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.contact-info p {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
    margin: 0;
}

/* ===== LOADING AND PROGRESS INDICATORS ===== */

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 300px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 218, 55, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-content p {
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    color: var(--text-color);
    margin: 0;
}

/* Download Progress Modal */
.download-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.download-modal {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.download-modal h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--cover-color);
    margin: 0 0 20px 0;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.book-info {
    text-align: center;
    margin-bottom: 25px;
}

.book-info h4 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 5px 0;
}

.book-info p {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.7;
    margin: 0;
}

.progress-container {
    margin-bottom: 25px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 218, 55, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #e6c533);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: var(--text-color);
}

.cancel-download {
    width: 100%;
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid #dc3545;
    color: #dc3545;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.cancel-download:hover {
    background: #dc3545;
    color: white;
}

/* Share Modal */
.share-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.share-modal {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
}

.share-modal h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--cover-color);
    margin: 0 0 20px 0;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.share-modal textarea {
    width: 100%;
    height: 120px;
    padding: 15px;
    border: 2px solid rgba(255, 218, 55, 0.3);
    border-radius: 8px;
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    resize: none;
    margin-bottom: 20px;
    background: rgba(255, 218, 55, 0.05);
}

.share-buttons {
    display: flex;
    gap: 15px;
}

.share-buttons button {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.share-buttons button:first-child {
    background: var(--primary-color);
    color: #000;
}

.share-buttons button:first-child:hover {
    background: #e6c533;
    transform: translateY(-2px);
}

.share-buttons button:last-child {
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid #dc3545;
    color: #dc3545;
}

.share-buttons button:last-child:hover {
    background: #dc3545;
    color: white;
}

/* ===== NOTIFICATION SYSTEM ===== */

#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left-color: #4CAF50;
}

.notification-error {
    border-left-color: #dc3545;
}

.notification-info {
    border-left-color: #2196F3;
}

.notification-warning {
    border-left-color: #FF9800;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification-success .notification-content i {
    color: #4CAF50;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-info .notification-content i {
    color: #2196F3;
}

.notification-warning .notification-content i {
    color: #FF9800;
}

.notification-content span {
    font-family: 'Cairo', sans-serif;
    font-size: 0.95rem;
    color: var(--text-color);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
}

/* Mobile responsiveness for modals */
@media (max-width: 768px) {
    .download-modal,
    .share-modal,
    .loading-content {
        margin: 20px;
        padding: 20px;
    }

    #notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification {
        padding: 12px 15px;
    }

    .notification-content span {
        font-size: 0.9rem;
    }
}

/* ===== PROGRESSIVE LOADING SYSTEM STYLES ===== */

/* Books Container Layout */
.books-container {
    width: 100%;
    position: relative;
    overflow: visible; /* 🎯 إزالة السكرول - السماح بالمحتوى الكامل */
    padding: 0 clamp(0.5rem, 2vw, 1rem);
    box-sizing: border-box;
    /* 🎯 التكيف مع ارتفاع المحتوى */
    height: auto;
    min-height: auto;
}

.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr));
    gap: clamp(1rem, 3vw, 2rem);
    padding: clamp(0.5rem, 2vw, 1rem);
    justify-items: center;
    /* 🎯 إزالة min-height وإضافة height: auto للتكيف مع المحتوى */
    height: auto;
    width: 100%;
    max-width: 100%;
    overflow: visible; /* 🎯 إزالة السكرول - السماح بالمحتوى الكامل */
    box-sizing: border-box;
    /* 🎯 استخدام متغير المسافة السفلية لاستيعاب flip-hint و navigation */
    padding-bottom: var(--grid-bottom-padding);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 2rem;
}

.section-title {
    font-family: 'Cairo', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-description {
    margin-top: 1rem;
}

.section-description p {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    color: var(--text-color);
    opacity: 0.8;
    margin: 0;
    line-height: 1.6;
}

/* Load More Button */
.load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem 2rem;
    margin-top: 2rem;
}

.load-more-btn {
    background: linear-gradient(135deg, var(--primary-color), #e6c533);
    border: none;
    color: #000;
    padding: 15px 30px;
    border-radius: 50px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 20px rgba(255, 218, 55, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 180px;
    justify-content: center;
}

.load-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.load-more-btn:hover::before {
    left: 100%;
}

.load-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(255, 218, 55, 0.5);
}

.load-more-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.load-more-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.load-more-btn:hover i {
    transform: scale(1.1);
}

/* Small Loading Spinner */
.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    border-top: 2px solid #000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* No Content Message */
.no-content-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 218, 55, 0.05);
    border: 2px dashed rgba(255, 218, 55, 0.3);
    border-radius: 15px;
    margin: 2rem;
}

.no-content-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.no-content-message h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 1rem 0;
}

.no-content-message p {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    color: var(--text-color);
    opacity: 0.7;
    margin: 0;
}

/* New Item Animation */
.flipbook-card.new-item {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Books Section Spacing */
.books-section {
    margin-bottom: 5rem;
    padding: 2rem 0;
}

.books-section:last-child {
    margin-bottom: 3rem;
}

/* Responsive Grid Adjustments */
@media (max-width: 1400px) {
    .books-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 1200px) {
    .books-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .books-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(140px, calc(33.333% - 1rem)), 1fr));
        gap: clamp(0.5rem, 2vw, 0.8rem);
        padding: clamp(0.25rem, 1vw, 0.5rem);
        justify-content: center;
    }

    .flipbook-card {
        width: 100% !important;
        height: auto !important;
        aspect-ratio: 3/4;
        max-width: min(140px, calc(33.333vw - 1rem));
        margin-bottom: 2rem !important;
        min-width: 120px; /* Ensure minimum readable size */
    }

    .flipbook-cover,
    .flipbook-page {
        width: 100% !important;
        height: 100% !important;
    }

    /* Mobile responsive text scaling */
    .flipbook-card {
        --base-font-size: 0.7rem;
        --title-scale: 1.2;
        --author-scale: 0.9;
        --description-scale: 0.75;
        --heading-scale: 0.95;
    }

    .flipbook-card .page-content {
        padding: 8px !important;
    }

    .flipbook-card .book-title {
        font-size: calc(var(--base-font-size) * 1.0) !important;
        line-height: 1.1 !important;
        margin-bottom: 4px !important;
    }

    .flipbook-card .book-author {
        font-size: calc(var(--base-font-size) * 0.85) !important;
        margin-bottom: 6px !important;
    }

    .description-text-content p {
        -webkit-line-clamp: 8;
    }

    .description-text {
        -webkit-line-clamp: 4;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-description p {
        font-size: 1rem;
    }

    .load-more-btn {
        padding: 12px 24px;
        font-size: 1rem;
        min-width: 160px;
    }

    .books-section {
        margin-bottom: 3rem;
        padding: 1rem 0;
    }

    /* Ensure flip-hint is visible during scroll */
    .flip-hint {
        bottom: -12px;
        font-size: 0.7rem;
        padding: 4px 8px;
        border-radius: 12px;
    }

    /* Mobile navigation */
    .flipbook-navigation {
        bottom: -20px;
        padding: 6px 12px;
        gap: 8px;
    }

    .nav-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .page-indicator {
        font-size: 0.7rem;
        min-width: 40px;
    }
}

@media (max-width: 480px) {
    .books-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.6rem;
        padding: 0.5rem;
    }

    .flipbook-card {
        width: 100% !important;
        height: auto !important;
        aspect-ratio: 3/4;
        max-width: 120px;
        margin-bottom: 1.5rem !important;
    }

    /* Extra small mobile responsive scaling */
    .flipbook-card {
        --base-font-size: 0.6rem;
        --title-scale: 1.1;
        --author-scale: 0.85;
        --description-scale: 0.7;
        --heading-scale: 0.9;
    }

    .flipbook-card .page-content {
        padding: 6px !important;
    }

    .flipbook-card .book-title {
        font-size: calc(var(--base-font-size) * 0.85) !important;
        line-height: 1.0 !important;
        margin-bottom: 3px !important;
    }

    .flipbook-card .book-author {
        font-size: calc(var(--base-font-size) * 0.7) !important;
        margin-bottom: 4px !important;
    }

    .description-text-content p {
        -webkit-line-clamp: 6;
    }

    .description-text {
        -webkit-line-clamp: 3;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-header {
        margin-bottom: 2rem;
        padding: 0 1rem;
    }

    .load-more-container {
        padding: 2rem 1rem;
    }

    .load-more-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        min-width: 140px;
    }

    /* Smaller flip-hint for mobile */
    .flip-hint {
        bottom: -10px;
        font-size: 0.65rem;
        padding: 3px 6px;
        border-radius: 10px;
    }

    /* Extra small navigation */
    .flipbook-navigation {
        bottom: -18px;
        padding: 4px 8px;
        gap: 6px;
    }

    .nav-btn {
        width: 24px;
        height: 24px;
        font-size: 0.65rem;
    }

    .page-indicator {
        font-size: 0.6rem;
        min-width: 35px;
    }

    .book-rating {
        top: 6px;
        right: 6px;
        padding: 2px 5px;
        font-size: 0.6rem;
        border-radius: 8px;
    }
}

/* ===== SMALL MOBILE (361px - 480px) - موبايل صغير ===== */
@media (max-width: 480px) and (min-width: 361px) {
    .books-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 0.55rem !important;
        padding: 0.45rem !important;
    }

    .flipbook-card {
        width: 100% !important;
        height: auto !important;
        aspect-ratio: 3/4;
        max-width: 115px;
        margin-bottom: 1.4rem !important;
        min-width: 105px;
    }

    .flipbook-card {
        --base-font-size: 0.58rem;
        --title-scale: 1.08;
        --author-scale: 0.83;
        --description-scale: 0.68;
        --heading-scale: 0.88;
    }

    .flipbook-card .page-content {
        padding: 5px !important;
    }

    .book-rating {
        top: 5px;
        right: 5px;
        padding: 2px 4px;
        font-size: 0.58rem;
        border-radius: 7px;
    }

    .actions-page {
        padding: 6px !important;
    }

    .action-btn {
        padding: 6px 5px !important;
        min-height: 47px !important;
        gap: 2px !important;
    }

    .action-btn span {
        font-size: 0.62rem !important;
    }

    .action-btn small {
        font-size: 0.48rem !important;
    }
}

/* ===== EXTRA SMALL MOBILE (280px - 360px) - موبايل صغير جداً ===== */
@media (max-width: 360px) and (min-width: 280px) {
    .books-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 0.4rem !important;
        padding: 0.3rem !important;
    }

    .flipbook-card {
        width: 100% !important;
        height: auto !important;
        aspect-ratio: 3/4;
        max-width: 105px;
        margin-bottom: 1.2rem !important;
        min-width: 95px;
    }

    .flipbook-card {
        --base-font-size: 0.55rem;
        --title-scale: 1.05;
        --author-scale: 0.8;
        --description-scale: 0.65;
        --heading-scale: 0.85;
    }

    .flipbook-card .page-content {
        padding: 4px !important;
    }

    .book-rating {
        top: 4px;
        right: 4px;
        padding: 1px 3px;
        font-size: 0.55rem;
        border-radius: 6px;
    }

    .actions-page {
        padding: 5px !important;
    }

    .action-btn {
        padding: 5px 4px !important;
        min-height: 42px !important;
        gap: 1px !important;
    }

    .action-btn span {
        font-size: 0.58rem !important;
    }

    .action-btn small {
        font-size: 0.45rem !important;
    }
}

/* ===== NARROW DEVICES - الأجهزة النحيفة ===== */

/* Narrow Tall Devices (aspect-ratio < 0.6) */
@media (max-aspect-ratio: 0.6) and (min-height: 600px) {
    .books-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: clamp(0.8rem, 2vw, 1.2rem) !important;
        padding: clamp(0.5rem, 1.5vw, 1rem) !important;
    }

    .flipbook-card {
        max-width: min(180px, calc(50% - 1rem));
        margin-bottom: 2.5rem !important;
        min-width: 140px;
    }

    .book-rating {
        top: 8px;
        right: 8px;
        padding: 3px 6px;
        font-size: 0.65rem;
        border-radius: 10px;
    }

    .actions-page {
        padding: 12px !important;
    }

    .action-btn {
        padding: 10px !important;
        min-height: 65px !important;
    }
}

/* Very Narrow Devices (aspect-ratio < 0.5) */
@media (max-aspect-ratio: 0.5) and (min-height: 700px) {
    .books-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: clamp(1rem, 2.5vw, 1.5rem) !important;
        padding: clamp(0.8rem, 2vw, 1.2rem) !important;
    }

    .flipbook-card {
        max-width: min(200px, calc(50% - 1.2rem));
        margin-bottom: 3rem !important;
        min-width: 160px;
    }

    .book-rating {
        top: 10px;
        right: 10px;
        padding: 4px 8px;
        font-size: 0.7rem;
        border-radius: 12px;
    }

    .actions-page {
        padding: 15px !important;
    }

    .action-btn {
        padding: 12px !important;
        min-height: 70px !important;
    }
}

/* Ultra Narrow Devices (aspect-ratio < 0.4) */
@media (max-aspect-ratio: 0.4) and (min-height: 800px) {
    .books-grid {
        grid-template-columns: 1fr !important;
        gap: clamp(1.5rem, 3vw, 2rem) !important;
        padding: clamp(1rem, 2.5vw, 1.5rem) !important;
        justify-items: center;
    }

    .flipbook-card {
        max-width: min(250px, calc(80vw));
        margin-bottom: 3.5rem !important;
        min-width: 200px;
    }

    .book-rating {
        top: 12px;
        right: 12px;
        padding: 5px 10px;
        font-size: 0.75rem;
        border-radius: 15px;
    }

    .actions-page {
        padding: 20px !important;
    }

    .action-btn {
        padding: 15px !important;
        min-height: 80px !important;
    }
}

/* Smooth scrolling for new content */
html {
    scroll-behavior: smooth;
}

/* Enhanced grid performance */
.books-grid {
    contain: layout style;
    will-change: contents;
}

/* 🎯 إلغاء التمرير الأفقي وجعل الشبكة تتكيف مع المحتوى */
.books-grid {
    /* إلغاء التمرير الأفقي نهائياً */
    display: grid !important;
    flex-direction: unset !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;

    /* جعل الشبكة تتكيف مع المحتوى */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;

    /* إعادة تطبيق إعدادات الشبكة */
    grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr)) !important;
    gap: clamp(1rem, 3vw, 2rem) !important;
    padding: clamp(0.5rem, 2vw, 1rem) !important;
    justify-items: center !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    padding-bottom: var(--grid-bottom-padding) !important;
}

/* إلغاء أي قواعد تمرير أفقي من styles.css */
.books-grid-container .books-grid {
    display: grid !important;
    flex-direction: unset !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
    scroll-behavior: unset !important;
    -webkit-overflow-scrolling: unset !important;
}

/* إلغاء قواعد التمرير الأفقي للشاشات المختلفة */
@media (max-width: 1200px) {
    .books-grid {
        display: grid !important;
        overflow-x: hidden !important;
        overflow-y: visible !important;
    }
}

@media (max-width: 768px) {
    .books-grid {
        display: grid !important;
        overflow-x: hidden !important;
        overflow-y: visible !important;
        grid-template-columns: repeat(auto-fit, minmax(min(140px, calc(33.333% - 1rem)), 1fr)) !important;
    }
}

@media (max-width: 480px) {
    .books-grid {
        display: grid !important;
        overflow-x: hidden !important;
        overflow-y: visible !important;
        grid-template-columns: repeat(auto-fit, minmax(min(120px, calc(50% - 0.5rem)), 1fr)) !important;
    }
}

@media (max-width: 320px) {
    .books-grid {
        display: grid !important;
        overflow-x: hidden !important;
        overflow-y: visible !important;
        grid-template-columns: repeat(auto-fit, minmax(min(100px, calc(50% - 0.5rem)), 1fr)) !important;
    }
}

/* ===== MODERN SCROLLBAR STYLING ===== */
.description-text-content::-webkit-scrollbar,
.description-text::-webkit-scrollbar {
    width: 6px;
}

.description-text-content::-webkit-scrollbar-track,
.description-text::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.description-text-content::-webkit-scrollbar-thumb,
.description-text::-webkit-scrollbar-thumb {
    background: rgba(255, 218, 55, 0.6);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.description-text-content::-webkit-scrollbar-thumb:hover,
.description-text::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 218, 55, 0.8);
}

/* Firefox scrollbar styling */
.description-text-content,
.description-text {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 218, 55, 0.6) rgba(255, 255, 255, 0.1);
}

/* ===== CSS CUSTOM PROPERTIES FOR RESPONSIVE DESIGN ===== */
:root {
    --grid-min-width: min(280px, calc(100vw - 2rem));
    --grid-gap: clamp(1rem, 3vw, 2rem);
    --card-padding: clamp(0.5rem, 2vw, 1rem);
    --text-scale-factor: clamp(0.8, 1.5vw, 1.2);

    /* 🎯 متغيرات المسافات - يمكن تعديلها بسهولة */
    --flip-hint-distance: -25px;        /* مسافة flip-hint من الكتاب */
    --navigation-distance: -30px;       /* مسافة navigation من الكتاب */
    --flip-hint-distance-tablet: -20px; /* مسافة flip-hint للتابلت */
    --navigation-distance-tablet: -25px; /* مسافة navigation للتابلت */
    --flip-hint-distance-mobile: -18px; /* مسافة flip-hint للموبايل */
    --navigation-distance-mobile: -22px; /* مسافة navigation للموبايل */
    --grid-bottom-padding: clamp(3rem, 6vw, 4rem); /* مسافة أسفل الشبكة */
}

/* ===== RTL SUPPORT ENHANCEMENTS ===== */
[dir="rtl"] .flipbook-navigation {
    direction: rtl;
}

[dir="rtl"] .nav-btn {
    transform: scaleX(-1);
}

[dir="rtl"] .nav-btn:hover {
    transform: scaleX(-1) scale(1.1);
}

[dir="rtl"] .description-text-content {
    text-align: right;
    padding-left: 0.5rem;
    padding-right: 0;
}

[dir="rtl"] .description-text-content::-webkit-scrollbar {
    left: 0;
    right: auto;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .flipbook-card,
    .flipbook-navigation,
    .nav-btn,
    .action-btn {
        transition: none !important;
        animation: none !important;
    }

    .flipbook-card.closed:hover {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .flipbook-navigation {
        background: rgba(0, 0, 0, 1) !important;
        border: 2px solid rgba(255, 218, 55, 1) !important;
    }

    .nav-btn {
        background: rgba(255, 218, 55, 1) !important;
        color: rgba(0, 0, 0, 1) !important;
    }
}
